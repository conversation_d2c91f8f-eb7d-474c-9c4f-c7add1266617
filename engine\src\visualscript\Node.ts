/**
 * 简化的视觉脚本节点基类
 * 用于第11批次和第12批次服务器节点
 */

export interface NodeInput {
  name: string;
  type: string;
  label: string;
  defaultValue?: any;
}

export interface NodeOutput {
  name: string;
  type: string;
  label: string;
}

/**
 * 简化的节点基类
 */
export class Node {
  /** 节点类型 */
  public readonly nodeType: string;
  
  /** 节点标题 */
  public readonly title: string;
  
  /** 节点描述 */
  public readonly description: string;
  
  /** 输入端口 */
  protected inputs: Map<string, NodeInput> = new Map();
  
  /** 输出端口 */
  protected outputs: Map<string, NodeOutput> = new Map();

  constructor(nodeType: string, title: string, description: string) {
    this.nodeType = nodeType;
    this.title = title;
    this.description = description;
  }

  /**
   * 添加输入端口
   */
  protected addInput(name: string, type: string, label: string, defaultValue?: any): void {
    this.inputs.set(name, { name, type, label, defaultValue });
  }

  /**
   * 添加输出端口
   */
  protected addOutput(name: string, type: string, label: string): void {
    this.outputs.set(name, { name, type, label });
  }

  /**
   * 获取输入端口
   */
  public getInputs(): NodeInput[] {
    return Array.from(this.inputs.values());
  }

  /**
   * 获取输出端口
   */
  public getOutputs(): NodeOutput[] {
    return Array.from(this.outputs.values());
  }

  /**
   * 获取输入端口
   */
  public getInput(name: string): NodeInput | null {
    return this.inputs.get(name) || null;
  }

  /**
   * 获取输出端口
   */
  public getOutput(name: string): NodeOutput | null {
    return this.outputs.get(name) || null;
  }

  /**
   * 执行节点
   * @param inputs 输入值
   * @returns 输出值
   */
  public async execute(inputs?: any): Promise<any> {
    // 子类实现
    return {};
  }

  /**
   * 验证输入
   */
  public validateInputs(inputs: any): boolean {
    for (const input of this.inputs.values()) {
      if (input.defaultValue === undefined && inputs[input.name] === undefined) {
        console.warn(`节点 ${this.title} 缺少必需的输入: ${input.name}`);
        return false;
      }
    }
    return true;
  }

  /**
   * 获取输入值（包含默认值）
   */
  protected getInputValue(inputs: any, name: string): any {
    const input = this.inputs.get(name);
    if (!input) return undefined;
    
    return inputs[name] !== undefined ? inputs[name] : input.defaultValue;
  }
}
