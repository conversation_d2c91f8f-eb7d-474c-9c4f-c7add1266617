/**
 * 第12批次节点验证脚本
 * 验证服务器集成扩展节点（331-350）的注册和基本功能
 */

const fs = require('fs');

console.log('🔍 第12批次节点验证开始...\n');

// 验证节点实现文件
function verifyNodeImplementation() {
  console.log('=== 📁 节点实现文件验证 ===');
  
  try {
    // 检查主实现文件
    const mainFile = 'engine/src/visualscript/presets/ServerNodesBatch12.ts';
    if (fs.existsSync(mainFile)) {
      const content = fs.readFileSync(mainFile, 'utf8');
      const nodeClasses = content.match(/export class \w+Node/g) || [];
      console.log(`✅ 主实现文件存在: ${mainFile}`);
      console.log(`📦 导出节点类数量: ${nodeClasses.length}个`);
      
      // 检查索引文件
      const indexFile = 'engine/src/visualscript/presets/ServerNodesBatch12Index.ts';
      if (fs.existsSync(indexFile)) {
        const indexContent = fs.readFileSync(indexFile, 'utf8');
        console.log(`✅ 索引文件存在: ${indexFile}`);
        
        // 检查映射配置
        if (indexContent.includes('BATCH12_NODE_MAPPING')) {
          console.log('✅ 节点映射配置已定义');
        }
        
        if (indexContent.includes('BATCH12_NODE_CONFIGS')) {
          console.log('✅ 节点配置信息已定义');
        }
      } else {
        console.log('❌ 索引文件不存在');
      }
      
      // 检查Node基类
      const nodeBaseFile = 'engine/src/visualscript/Node.ts';
      if (fs.existsSync(nodeBaseFile)) {
        console.log(`✅ Node基类文件存在: ${nodeBaseFile}`);
      } else {
        console.log('❌ Node基类文件不存在');
      }
      
    } else {
      console.log('❌ 主实现文件不存在');
    }
  } catch (error) {
    console.error('❌ 验证节点实现文件失败:', error.message);
  }
}

// 验证引擎集成
function verifyEngineIntegration() {
  console.log('\n=== 🔧 引擎集成验证 ===');
  
  try {
    const integrationFile = 'editor/src/services/EngineNodeIntegration.ts';
    if (fs.existsSync(integrationFile)) {
      const content = fs.readFileSync(integrationFile, 'utf8');
      
      // 检查初始化方法中的批次12调用
      if (content.includes('this.registerBatch12Nodes()')) {
        console.log('✅ 初始化方法中已添加第12批次注册调用');
      } else {
        console.log('❌ 初始化方法中缺少第12批次注册调用');
      }
      
      // 检查注册方法
      if (content.includes('private registerBatch12Nodes()')) {
        console.log('✅ 第12批次注册方法已定义');
        
        // 检查子方法
        if (content.includes('registerAssetExtensionNodes')) {
          console.log('✅ 资产扩展节点注册方法已定义');
        }
        
        if (content.includes('registerCollaborationNodes')) {
          console.log('✅ 协作服务节点注册方法已定义');
        }
      } else {
        console.log('❌ 第12批次注册方法未定义');
      }
      
    } else {
      console.log('❌ 引擎集成文件不存在');
    }
  } catch (error) {
    console.error('❌ 验证引擎集成失败:', error.message);
  }
}

// 验证编辑器注册
function verifyEditorRegistration() {
  console.log('\n=== 🎨 编辑器注册验证 ===');
  
  try {
    const registryFile = 'editor/src/services/NodeRegistryService.ts';
    if (fs.existsSync(registryFile)) {
      const content = fs.readFileSync(registryFile, 'utf8');
      
      // 检查初始化方法中的批次12调用
      if (content.includes('this.initializeBatch12Nodes()')) {
        console.log('✅ 初始化方法中已添加第12批次注册调用');
      } else {
        console.log('❌ 初始化方法中缺少第12批次注册调用');
      }
      
      // 检查注册方法
      if (content.includes('private initializeBatch12Nodes()')) {
        console.log('✅ 第12批次注册方法已定义');
        
        // 检查具体节点注册
        const batch12Nodes = [
          'server/asset/updateAssetInfo',
          'server/asset/moveAssetToFolder',
          'server/collaboration/joinRoom',
          'server/collaboration/syncState'
        ];
        
        let registeredCount = 0;
        batch12Nodes.forEach(nodeType => {
          if (content.includes(`type: '${nodeType}'`)) {
            registeredCount++;
          }
        });
        
        console.log(`✅ 样本节点注册检查: ${registeredCount}/${batch12Nodes.length}个`);
      } else {
        console.log('❌ 第12批次注册方法未定义');
      }
      
    } else {
      console.log('❌ 编辑器注册文件不存在');
    }
  } catch (error) {
    console.error('❌ 验证编辑器注册失败:', error.message);
  }
}

// 验证节点数量
function verifyNodeCount() {
  console.log('\n=== 📊 节点数量验证 ===');
  
  try {
    const mainFile = 'engine/src/visualscript/presets/ServerNodesBatch12.ts';
    if (fs.existsSync(mainFile)) {
      const content = fs.readFileSync(mainFile, 'utf8');
      
      // 统计节点类
      const nodeClasses = content.match(/export class \w+Node extends Node/g) || [];
      console.log(`📦 实现的节点类数量: ${nodeClasses.length}个`);
      
      // 检查预期的20个节点
      const expectedNodes = [
        'UpdateAssetInfoNode', 'MoveAssetToFolderNode', 'CreateAssetFolderNode', 'DeleteAssetFolderNode',
        'ShareAssetNode', 'GetAssetVersionsNode', 'CreateAssetVersionNode', 'RestoreAssetVersionNode',
        'GenerateAssetThumbnailNode', 'OptimizeAssetNode',
        'JoinCollaborationRoomNode', 'LeaveCollaborationRoomNode', 'SendCollaborationOperationNode',
        'ReceiveCollaborationOperationNode', 'ResolveCollaborationConflictNode', 'GetOnlineUsersNode',
        'BroadcastCollaborationMessageNode', 'LockCollaborationResourceNode', 'UnlockCollaborationResourceNode',
        'SyncCollaborationStateNode'
      ];
      
      let foundCount = 0;
      expectedNodes.forEach(nodeName => {
        if (content.includes(`export class ${nodeName}`)) {
          foundCount++;
        }
      });
      
      console.log(`✅ 预期节点检查: ${foundCount}/${expectedNodes.length}个`);
      
      if (foundCount === expectedNodes.length) {
        console.log('🎉 第12批次所有20个节点都已实现！');
      } else {
        console.log(`⚠️ 还有 ${expectedNodes.length - foundCount} 个节点未实现`);
      }
      
    } else {
      console.log('❌ 主实现文件不存在，无法验证节点数量');
    }
  } catch (error) {
    console.error('❌ 验证节点数量失败:', error.message);
  }
}

// 生成验证报告
function generateReport() {
  console.log('\n=== 📋 验证报告 ===');
  
  const report = {
    timestamp: new Date().toISOString(),
    batch: 12,
    nodeRange: '331-350',
    expectedCount: 20,
    categories: ['资产服务扩展', '协作服务'],
    status: 'completed'
  };
  
  console.log(`验证时间: ${report.timestamp}`);
  console.log(`批次信息: 第${report.batch}批次 (${report.nodeRange})`);
  console.log(`预期节点数: ${report.expectedCount}个`);
  console.log(`节点分类: ${report.categories.join(', ')}`);
  console.log(`验证状态: ${report.status}`);
  
  // 保存报告
  try {
    fs.writeFileSync('batch12-verification-report.json', JSON.stringify(report, null, 2));
    console.log('✅ 验证报告已保存到 batch12-verification-report.json');
  } catch (error) {
    console.error('❌ 保存验证报告失败:', error.message);
  }
}

// 执行所有验证
function runAllVerifications() {
  verifyNodeImplementation();
  verifyEngineIntegration();
  verifyEditorRegistration();
  verifyNodeCount();
  generateReport();
  
  console.log('\n🎯 第12批次节点验证完成！');
  console.log('📝 如果所有检查都通过，第12批次节点已成功集成到系统中');
}

// 运行验证
runAllVerifications();
